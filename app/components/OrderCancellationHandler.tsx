"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { X, Trash2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { OrderDocument, OrderItem } from '@/lib/db/v4/schemas/order-schema';
import { processDeliveryFailure, updateOrder } from '@/lib/db/v4/operations/order-ops';
import { kitchenQueueService } from '@/lib/services/kitchen-queue-service';

interface OrderCancellationHandlerProps {
  order: OrderDocument;
  onSuccess?: () => void;
  trigger?: React.ReactNode;
}

interface WastedItemState {
  itemIndex: number;
  item: OrderItem;
  wastedQuantity: number;
  isSelected: boolean;
}

const CANCELLATION_REASONS = [
  'Client a annulé',
  'Erreur de commande',
  'Problème de stock',
  'Problème de qualité',
  'Erreur de prix',
  'Demande du client',
  'Autre'
];

export function OrderCancellationHandler({ order, onSuccess, trigger }: OrderCancellationHandlerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [cancellationType, setCancellationType] = useState<'simple' | 'with_waste'>('simple');
  const [wastedItems, setWastedItems] = useState<WastedItemState[]>([]);
  const [cancellationReason, setCancellationReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  // Initialize wasted items when dialog opens
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (open) {
      // Initialize all items as potentially wasted
      const initialWastedItems: WastedItemState[] = order.items.map((item, index) => ({
        itemIndex: index,
        item,
        wastedQuantity: item.quantity,
        isSelected: false
      }));
      setWastedItems(initialWastedItems);
      setCancellationType('simple');
      setCancellationReason('');
      setCustomReason('');
    }
  };

  // Update wasted quantity for an item
  const updateWastedQuantity = (itemIndex: number, quantity: number) => {
    setWastedItems(prev => prev.map(wastedItem => 
      wastedItem.itemIndex === itemIndex 
        ? { ...wastedItem, wastedQuantity: Math.max(0, Math.min(quantity, wastedItem.item.quantity)) }
        : wastedItem
    ));
  };

  // Toggle item selection
  const toggleItemSelection = (itemIndex: number, selected: boolean) => {
    setWastedItems(prev => prev.map(wastedItem => 
      wastedItem.itemIndex === itemIndex 
        ? { ...wastedItem, isSelected: selected }
        : wastedItem
    ));
  };

  // Select all items
  const selectAllItems = () => {
    setWastedItems(prev => prev.map(item => ({ ...item, isSelected: true })));
  };

  // Deselect all items
  const deselectAllItems = () => {
    setWastedItems(prev => prev.map(item => ({ ...item, isSelected: false })));
  };

  // Get selected items for waste processing
  const selectedItems = wastedItems.filter(item => item.isSelected && item.wastedQuantity > 0);

  // Process order cancellation
  const processCancellation = async () => {
    const reason = cancellationReason === 'Autre' ? customReason : cancellationReason;
    if (!reason.trim()) {
      toast({
        variant: "destructive",
        title: "Raison requise",
        description: "Veuillez spécifier la raison de l'annulation"
      });
      return;
    }

    try {
      setIsProcessing(true);

      if (cancellationType === 'simple') {
        // Simple cancellation - just update order status
        await updateOrder(order._id, { status: 'cancelled' });
        await kitchenQueueService.cancelOrder(order._id);
        
        toast({
          title: "Commande annulée",
          description: `Commande #${order._id.slice(-6)} annulée sans gaspillage`
        });
      } else {
        // Cancellation with waste - use delivery failure system
        if (selectedItems.length === 0) {
          toast({
            variant: "destructive",
            title: "Aucun article sélectionné",
            description: "Veuillez sélectionner au moins un article gaspillé"
          });
          return;
        }

        const wasteData = selectedItems.map(item => ({
          itemIndex: item.itemIndex,
          quantity: item.wastedQuantity
        }));

        // Use processDeliveryFailure for waste tracking
        await processDeliveryFailure(
          order._id,
          wasteData,
          `Annulation avec gaspillage: ${reason}`,
          'Staff'
        );

        // Update order status to cancelled
        await updateOrder(order._id, { status: 'cancelled' });
        await kitchenQueueService.cancelOrder(order._id);

        toast({
          title: "Commande annulée avec gaspillage",
          description: `${selectedItems.length} article(s) marqué(s) comme gaspillage`
        });
      }

      setIsOpen(false);
      onSuccess?.();
    } catch (error) {
      console.error('Error processing order cancellation:', error);
      toast({
        variant: "destructive",
        title: "Erreur",
        description: "Impossible d'annuler la commande"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm" className="gap-2 text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300">
      <X className="h-4 w-4" />
      Annuler
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <X className="h-5 w-5 text-red-500" />
            Annuler la commande
          </DialogTitle>
          <DialogDescription>
            Choisissez le type d'annulation selon que la nourriture a été préparée ou non.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Cancellation Type Selection */}
          <div className="space-y-3">
            <Label>Type d'annulation</Label>
            <RadioGroup value={cancellationType} onValueChange={(value: 'simple' | 'with_waste') => setCancellationType(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="simple" id="simple" />
                <Label htmlFor="simple" className="flex items-center gap-2">
                  <X className="h-4 w-4 text-gray-500" />
                  <div>
                    <div className="font-medium">Annulation simple</div>
                    <div className="text-xs text-muted-foreground">La nourriture n'a pas encore été préparée</div>
                  </div>
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="with_waste" id="with_waste" />
                <Label htmlFor="with_waste" className="flex items-center gap-2">
                  <Trash2 className="h-4 w-4 text-orange-500" />
                  <div>
                    <div className="font-medium">Annulation avec gaspillage</div>
                    <div className="text-xs text-muted-foreground">Certains articles ont déjà été préparés</div>
                  </div>
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Reason Selection */}
          <div className="space-y-2">
            <Label>Raison de l'annulation</Label>
            <RadioGroup value={cancellationReason} onValueChange={setCancellationReason}>
              {CANCELLATION_REASONS.map((reason) => (
                <div key={reason} className="flex items-center space-x-2">
                  <RadioGroupItem value={reason} id={reason} />
                  <Label htmlFor={reason} className="text-sm">{reason}</Label>
                </div>
              ))}
            </RadioGroup>

            {cancellationReason === 'Autre' && (
              <Textarea
                placeholder="Spécifiez la raison..."
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                className="mt-2"
              />
            )}
          </div>

          {/* Waste Item Selection - Only show for waste cancellation */}
          {cancellationType === 'with_waste' && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label>Articles gaspillés</Label>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={selectAllItems}
                    className="h-7 text-xs"
                    type="button"
                  >
                    Tout sélectionner
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={deselectAllItems}
                    className="h-7 text-xs"
                    type="button"
                  >
                    Tout désélectionner
                  </Button>
                </div>
              </div>

              <div className="space-y-2 max-h-60 overflow-y-auto">
                {wastedItems.map((wastedItem) => {
                  const itemPrice = wastedItem.item.price + (wastedItem.item.addons?.reduce((sum, addon) => sum + addon.price, 0) || 0);
                  const itemTotal = itemPrice * wastedItem.wastedQuantity;

                  return (
                    <div
                      key={wastedItem.itemIndex}
                      className={cn(
                        "p-3 border rounded-lg transition-colors",
                        wastedItem.isSelected ? "bg-red-50 border-red-200" : "bg-background"
                      )}
                    >
                      <div className="flex items-start gap-3">
                        <Checkbox
                          checked={wastedItem.isSelected}
                          onCheckedChange={(checked) => toggleItemSelection(wastedItem.itemIndex, !!checked)}
                          className="mt-1"
                        />

                        <div className="flex-1 space-y-2">
                          <div className="flex items-start justify-between">
                            <div>
                              <div className="font-medium text-sm">{wastedItem.item.name}</div>
                              {wastedItem.item.addons && wastedItem.item.addons.length > 0 && (
                                <div className="text-xs text-muted-foreground">
                                  + {wastedItem.item.addons.map(addon => addon.name).join(', ')}
                                </div>
                              )}
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {itemTotal.toFixed(2)} DA
                            </Badge>
                          </div>

                          <div className="flex items-center gap-2">
                            <Label className="text-xs">Quantité gaspillée:</Label>
                            <div className="flex items-center gap-1">
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={() => updateWastedQuantity(wastedItem.itemIndex, wastedItem.wastedQuantity - 1)}
                                disabled={wastedItem.wastedQuantity <= 0}
                                type="button"
                              >
                                -
                              </Button>
                              <span className="text-sm font-medium w-8 text-center">
                                {wastedItem.wastedQuantity}
                              </span>
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={() => updateWastedQuantity(wastedItem.itemIndex, wastedItem.wastedQuantity + 1)}
                                disabled={wastedItem.wastedQuantity >= wastedItem.item.quantity}
                                type="button"
                              >
                                +
                              </Button>
                              <span className="text-xs text-muted-foreground ml-1">
                                / {wastedItem.item.quantity}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {selectedItems.length > 0 && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="text-sm font-medium text-red-800">
                    Résumé du gaspillage: {selectedItems.length} article(s) sélectionné(s)
                  </div>
                  <div className="text-xs text-red-600 mt-1">
                    Total gaspillé: {selectedItems.reduce((sum, item) => {
                      const itemPrice = item.item.price + (item.item.addons?.reduce((sum, addon) => sum + addon.price, 0) || 0);
                      return sum + (itemPrice * item.wastedQuantity);
                    }, 0).toFixed(2)} DA
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isProcessing}>
            Annuler
          </Button>
          <Button
            variant="destructive"
            onClick={processCancellation}
            disabled={isProcessing || !cancellationReason || (cancellationReason === 'Autre' && !customReason.trim())}
          >
            {isProcessing ? 'Traitement...' :
             cancellationType === 'simple' ? 'Annuler la commande' : 'Annuler avec gaspillage'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
